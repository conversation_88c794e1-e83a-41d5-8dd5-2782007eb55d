<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Smart_Booking {

    private static $instance = null;

    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_shortcode( 'ochandydude_booking_form', [ $this, 'render_wrapper_shortcode' ] );
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
        add_action( 'wp_ajax_nopriv_ochd_check_user_exists', [ $this, 'ajax_check_user_exists' ] );
        add_action( 'wp_ajax_ochd_check_user_exists', [ $this, 'ajax_check_user_exists' ] );
    }

    private function get_clean_user_meta($user_id, $key) {
        $meta_value = get_user_meta($user_id, $key, true);
        if (is_array($meta_value)) {
            return !empty($meta_value) ? (string) $meta_value[0] : '';
        }
        return (string) $meta_value;
    }

    public function enqueue_assets() {
        global $post;
        if ( is_front_page() || ( is_a( $post, 'WP_Post' ) && has_shortcode( $post->post_content, 'ochandydude_booking_form' ) ) ) {
            wp_enqueue_style( 'ochd-booking-form-style', OCHD_MASTER_PLUGIN_URL . 'assets/css/booking-form.css', [], '1.0' );
            wp_enqueue_script('ochd-booking-wrapper-script', OCHD_MASTER_PLUGIN_URL . 'assets/js/booking-form.js', [], '21.1', true);
            $data_for_js = [
                'isLoggedIn' => is_user_logged_in(),
                'ajax_url'   => admin_url( 'admin-ajax.php' ),
                'login_url'  => do_shortcode('[openid_connect_generic_auth_url]'),
                'nonce'      => wp_create_nonce('wp_ajax'), 
            ];
            if ( is_user_logged_in() ) {
                $user = wp_get_current_user();
                $data_for_js['userData'] = [
                    'first_name' => $user->first_name,
                    'last_name'  => $user->last_name,
                    'email'      => $user->user_email,
                    'phone'      => $this->get_clean_user_meta( $user->ID, 'billing_phone' ),
                    'address'    => $this->get_clean_user_meta( $user->ID, 'billing_address_1' ),
                    'city'       => $this->get_clean_user_meta( $user->ID, 'billing_city' ),
                    'zip'        => $this->get_clean_user_meta( $user->ID, 'billing_postcode' ),
                ];
            }
            wp_localize_script( 'ochd-booking-wrapper-script', 'ochdBookingData', $data_for_js );
        }
    }

    public function render_wrapper_shortcode() {
        ob_start(); ?>
        <div class="ochd-booking-container" style="position: relative; line-height: 0;">
            <div class="ochd-booking-form-wrapper">
                <?php echo do_shortcode( '[easyappointments]' ); ?>
            </div>
            <?php if ( ! is_user_logged_in() ) : ?>
                <div id="ochd-guest-overlay" class="ochd-guest-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 10; display: flex; align-items: center; justify-content: center; background-color: rgba(30, 30, 30, 0.75); backdrop-filter: blur(8px); color: #eee;">
                    <div style="text-align: center; padding: 20px; max-width: 90%; width: 100%; max-width: 500px;">
                        <h3 style="margin-bottom: 15px; font-size: 24px;">Welcome!</h3>
                        <p style="margin-bottom: 25px; font-size: 16px; line-height: 1.4;">Log in for a faster booking experience or continue as a guest.</p>
                        <div style="display: flex; flex-direction: column; gap: 15px; align-items: center;">
                            <a href="<?php echo esc_url(do_shortcode('[openid_connect_generic_auth_url]')); ?>" class="ochd-button ochd-register-button">Login / Register</a>
                            <button id="ochd-continue-as-guest" class="ochd-button ochd-guest-button">Continue as Guest</button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <style>
            .ochd-booking-form-wrapper {
                /* Мягкие отступы внутри контейнера для формы */
                padding-left: 8px;
                padding-right: 8px;
                background-color: transparent;
                border-radius: 88px;
            }

            /* Исправление: убираем вертикальные отступы у формы, чтобы избежать лишних wrapper */
            .ochd-booking-form-wrapper > * {
                margin-top: 0 !important;
                margin-bottom: 0 !important;
            }

            .ochd-booking-form-wrapper iframe.easyappointments-iframe {
                display: block;
                border: none;
                width: 100%;
                border-radius: 80px;
            }
            #ochd-guest-overlay {
                 border-radius: 88px;
            }
            .ochd-button {
                display: inline-block;
                padding: 15px 30px;
                border-radius: 6px;
                text-decoration: none !important;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                border: 2px solid transparent;
                transition: all 0.3s ease;
                width: 100% !important;
                max-width: 280px !important;
                min-width: 280px;
                text-align: center;
                box-sizing: border-box;
                line-height: 1.2;
                white-space: nowrap;
            }
            .ochd-register-button {
                background-color: #007bff !important;
                color: white !important;
                border-color: #007bff !important;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
            }
            .ochd-register-button:hover {
                background-color: #0056b3 !important;
                border-color: #0056b3 !important;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
                text-decoration: none !important;
                color: white !important;
            }
            .ochd-guest-button {
                background-color: transparent !important;
                border-color: #6c757d !important;
                color: #eee !important;
            }
            .ochd-guest-button:hover {
                background-color: rgba(255,255,255,0.1) !important;
                border-color: #adb5bd !important;
                transform: translateY(-1px);
                color: #eee !important;
            }

            /* Mobile responsive styling */
            @media (max-width: 768px) {
                #ochd-guest-overlay h3 {
                    font-size: 20px !important;
                }
                #ochd-guest-overlay p {
                    font-size: 14px !important;
                    line-height: 1.5 !important;
                }
                .ochd-button {
                    font-size: 14px !important;
                    padding: 12px 25px !important;
                }
            }
        </style>

        <?php return ob_get_clean();
    }

    public function ajax_check_user_exists() {
        header("Access-control-allow-origin: https://book.ochandydude.pro");
        header("Access-Control-Allow-Methods: POST, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type");
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') { exit(0); }
        check_ajax_referer('wp_ajax', 'security');
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
        $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
        if (empty($email) && empty($phone)) { wp_send_json_error(['message' => 'Email or phone required.']); }
        $user_exists = false;
        if (!empty($email) && email_exists($email)) { $user_exists = true; }
        if (!$user_exists && !empty($phone)) {
            $users = get_users(['meta_key' => 'billing_phone', 'meta_value' => $phone, 'number' => 1, 'fields' => 'ID']);
            if (!empty($users)) { $user_exists = true; }
        }
        wp_send_json_success(['user_exists' => $user_exists]);
    }
}