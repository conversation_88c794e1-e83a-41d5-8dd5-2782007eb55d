<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_SSO_Integration {

    const OIDC_USER_CLAIM_META_KEY = 'openid-connect-generic-last-user-claim';

    public function __construct() {
        add_filter( 'logout_url', [ $this, 'replace_logout_url' ], 10, 2 );
        add_action( 'init', [ $this, 'handle_custom_logout_request' ] );
        add_action( 'openid-connect-generic-user-logged-in', [ $this, 'handle_user_login_sync' ], 10, 1 );
        
        if ( ! is_admin() ) {
            add_action( 'template_redirect', [ $this, 'force_keycloak_login' ] );
            add_action( 'wp_enqueue_scripts', [ $this, 'pass_data_to_javascript' ] );
        }
    }

    public function replace_logout_url( $logout_url, $redirect ) {
        $nonce = wp_create_nonce( 'custom_logout_nonce' );
        return home_url( '/?custom-logout=true&_wpnonce=' . $nonce );
    }

    public function handle_custom_logout_request() {
        if ( isset( $_GET['custom-logout'] ) && isset( $_GET['_wpnonce'] ) && wp_verify_nonce( $_GET['_wpnonce'], 'custom_logout_nonce' ) ) {
            
            $user_id = get_current_user_id();
            $id_token = get_user_meta( $user_id, 'openid-connect-generic-id-token', true );

            wp_logout();

            $oidc_settings = get_option('openid_connect_generic_settings');
            $end_session_endpoint = isset($oidc_settings['end_session_endpoint']) ? $oidc_settings['end_session_endpoint'] : '';

            if ( ! empty($end_session_endpoint) ) {
                $post_logout_redirect_uri = home_url();
                
                $keycloak_logout_url = add_query_arg([
                    'post_logout_redirect_uri' => urlencode($post_logout_redirect_uri),
                    'id_token_hint'            => $id_token,
                ], $end_session_endpoint);
                
                wp_safe_redirect( $keycloak_logout_url );
                exit;
            }

            wp_safe_redirect( home_url() );
            exit;
        }
    }

    public function handle_user_login_sync( $user ) {
        if ( ! $user instanceof WP_User ) return;
        
        $claims = get_user_meta( $user->ID, self::OIDC_USER_CLAIM_META_KEY, true );
        if ( empty( $claims ) || ! is_array( $claims ) ) return;

        if ( isset( $claims['email'] ) && $user->user_email !== $claims['email'] ) {
            wp_update_user( [
                'ID'         => $user->ID,
                'user_email' => sanitize_email( $claims['email'] ),
            ] );
        }

        $this->sync_user_roles( $user, $claims );
        $this->sync_user_meta_data( $user->ID, $claims );
    }

    private function sync_user_roles( $user, $claims ) {
        $keycloak_roles = isset( $claims['roles'] ) ? (array) $claims['roles'] : [];
        if ( empty( $keycloak_roles ) ) return;
        
        $target_role = 'customer';
        if ( in_array( 'admin', $keycloak_roles, true ) ) {
            $target_role = 'administrator';
        } elseif ( in_array( 'provider', $keycloak_roles, true ) ) {
            $target_role = 'editor';
        }
        
        if ( ! in_array( $target_role, $user->roles, true ) ) {
            $user->set_role( $target_role );
        }
    }

    private function sync_user_meta_data( $user_id, $claims ) {
        $flat_claims_map = [
            'given_name'   => 'first_name',
            'family_name'  => 'last_name',
            'email'        => 'billing_email',
            'phone_number' => 'billing_phone',
        ];
        foreach ( $flat_claims_map as $claim_key => $meta_key ) {
            if ( isset( $claims[ $claim_key ] ) ) {
                $this->update_user_meta_if_changed($user_id, $meta_key, $claims[$claim_key]);
            }
        }
        
        if ( isset( $claims['address'] ) && is_array( $claims['address'] ) ) {
            $address_claim = $claims['address'];
            $address_map = [
                'street_address' => 'billing_address_1',
                'locality'       => 'billing_city',
                'postal_code'    => 'billing_postcode',
                'region'         => 'billing_state',
                'country'        => 'billing_country',
            ];
            foreach ($address_map as $claim_key => $meta_key) {
                if ( isset( $address_claim[$claim_key] ) ) {
                    $this->update_user_meta_if_changed($user_id, $meta_key, $address_claim[$claim_key]);
                }
            }
        }
    }

    private function update_user_meta_if_changed($user_id, $meta_key, $new_value) {
        $new_value = sanitize_text_field($new_value);
        if ( get_user_meta($user_id, $meta_key, true) !== $new_value ) {
            update_user_meta($user_id, $meta_key, $new_value);
            if (strpos($meta_key, 'billing_') === 0) {
                update_user_meta( $user_id, str_replace('billing_', 'shipping_', $meta_key), $new_value );
            }
        }
    }

    public function force_keycloak_login() {
        global $pagenow;
        if ( 'wp-login.php' !== $pagenow && is_page('my-account') && ! is_user_logged_in() ) {
            $login_url = do_shortcode('[openid_connect_generic_auth_url]');
            if ( ! empty($login_url) ) {
                wp_redirect( $login_url );
                exit;
            }
        }
    }

    public function pass_data_to_javascript() {
        wp_register_script( 'ochandydude-sso-helper', false );
        wp_enqueue_script( 'ochandydude-sso-helper' );
        $login_url = do_shortcode('[openid_connect_generic_auth_url]');
        wp_localize_script( 'ochandydude-sso-helper', 'ssoHandlerData', [
            'isLoggedIn' => is_user_logged_in(),
            'loginUrl'   => $login_url,
        ]);
    }
}