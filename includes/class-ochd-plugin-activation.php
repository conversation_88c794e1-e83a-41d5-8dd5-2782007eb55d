<?php

if ( ! defined( 'ABSPATH' ) ) exit;

class OCHD_Plugin_Activation {

    public static function create_plugin_pages() {
        $pages = [
            'ochd_profile_page_id' => [
                'title' => 'My Profile',
                'content' => '[ochd_profile_content]'
            ],
            'ochd_bookings_page_id' => [
                'title' => 'My Bookings',
                'content' => '[ochd_bookings_content]'
            ]
        ];

        foreach ($pages as $option_name => $page_data) {
            $page_id = get_option( $option_name );
            $page_exists = ($page_id) ? get_post($page_id) : null;

            if ( ! $page_exists ) {
                $page = get_page_by_title( $page_data['title'] );
                if ( ! $page ) {
                    $new_page_id = wp_insert_post([
                        'post_title'     => $page_data['title'],
                        'post_content'   => $page_data['content'],
                        'post_status'    => 'publish',
                        'post_author'    => 1,
                        'post_type'      => 'page',
                        'comment_status' => 'closed',
                        'ping_status'    => 'closed',
                    ]);
                    update_option( $option_name, $new_page_id );
                } else {
                    update_option( $option_name, $page->ID );
                }
            }
        }
    }
}
