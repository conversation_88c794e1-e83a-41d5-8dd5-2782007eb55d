<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class OCHandyDude_Profile_Shortcodes {

    public function __construct() {
        add_shortcode( 'ochandydude_profile_menu', [ $this, 'render_profile_menu' ] );
        add_shortcode( 'ochd_profile_content', [ $this, 'render_profile_page_content' ] );
        add_shortcode( 'ochd_bookings_content', [ $this, 'render_bookings_page_content' ] );
    }

    /**
     * Renders the profile menu.
     */
    public function render_profile_menu() {
        ob_start();
        $login_url = do_shortcode('[openid_connect_generic_auth_url]');
        ?>
        <div class="ochd-profile-menu ochd-unified-icon-wrapper">
            <div class="ochd-profile-icon" aria-label="User Profile">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div class="ochd-profile-dropdown">
                <?php if ( is_user_logged_in() ) : ?>
                    <?php
                    $current_user = wp_get_current_user();
                    $profile_page_url = get_permalink( get_option('ochd_profile_page_id') );
                    $bookings_page_url = get_permalink( get_option('ochd_bookings_page_id') );
                    $keycloak_account_url = apply_filters( 'ochd_keycloak_account_url', 'https://auth.ochandydude.pro/realms/ochandydude/account/' );
                    ?>
                    <div class="ochd-dropdown-header">
                        <strong><?php echo esc_html( $current_user->display_name ); ?></strong>
                        <small><?php echo esc_html( $current_user->user_email ); ?></small>
                    </div>
                    <a href="<?php echo esc_url( $profile_page_url ); ?>">My Profile</a>
                    <a href="<?php echo esc_url( $bookings_page_url ); ?>">My Bookings</a>
                    <a href="<?php echo esc_url( $keycloak_account_url ); ?>" target="_blank" rel="noopener noreferrer">Account Settings</a>
                    <div class="ochd-dropdown-footer">
                        <a href="<?php echo esc_url( wp_logout_url( home_url() ) ); ?>">Logout</a>
                    </div>
                <?php else : ?>
                    <div class="ochd-guest-menu">
                         <a href="<?php echo esc_url( $login_url ); ?>" class="ochd-guest-login-button">Login / Register</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Renders the profile page content.
     */
    public function render_profile_page_content() {
        if ( ! is_user_logged_in() ) { 
            return '<p>Please log in to view your profile.</p>'; 
        }
        $user = wp_get_current_user();
        $user_id = $user->ID;
        $keycloak_account_url = apply_filters( 'ochd_keycloak_account_url', 'https://auth.ochandydude.pro/realms/ochandydude/account/' );

        ob_start();
        ?>
        <div class="ochd-profile-container">
            <h2>My Profile</h2>
            <div class="ochd-profile-card">
                <h3>Contact Information</h3>
                <p><strong>Name:</strong> <?php echo esc_html( get_user_meta( $user_id, 'first_name', true ) . ' ' . get_user_meta( $user_id, 'last_name', true ) ); ?></p>
                <p><strong>Email:</strong> <?php echo esc_html( $user->user_email ); ?></p>
                <p><strong>Phone:</strong> <?php echo esc_html( get_user_meta( $user_id, 'billing_phone', true ) ); ?></p>
            </div>
            <div class="ochd-profile-card">
                <h3>Billing Address</h3>
                <p>
                    <?php echo esc_html( get_user_meta( $user_id, 'billing_address_1', true ) ); ?><br>
                    <?php echo esc_html( get_user_meta( $user_id, 'billing_city', true ) . ', ' . get_user_meta( $user_id, 'billing_state', true ) . ' ' . get_user_meta( $user_id, 'billing_postcode', true ) ); ?><br>
                    <?php echo esc_html( get_user_meta( $user_id, 'billing_country', true ) ); ?>
                </p>
            </div>
            <p><a href="<?php echo esc_url( $keycloak_account_url ); ?>" target="_blank" rel="noopener noreferrer">Edit your profile information on our main account server.</a></p>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Renders the bookings page content.
     */
    public function render_bookings_page_content() {
        if ( ! is_user_logged_in() ) { 
            return '<p>Please log in to view your bookings.</p>'; 
        }

        wp_enqueue_script(
            'ochandydude-profile-tabs-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/profile-tabs.js',
            [],
            '1.0',
            true
        );

        ob_start();
        ?>
        <div class="ochd-bookings-container">
            <h2>My Bookings & Orders</h2>
            <div class="ochd-tabs">
                <button class="ochd-tab-link active" onclick="openOchdTab(event, 'upcoming')">Upcoming Bookings</button>
                <button class="ochd-tab-link" onclick="openOchdTab(event, 'history')">Booking History</button>
                <button class="ochd-tab-link" onclick="openOchdTab(event, 'invoices')">Invoices</button>
            </div>
            <div id="upcoming" class="ochd-tab-content" style="display:block;">
                <h3>Upcoming Bookings</h3>
                <p>Here you will see your future appointments. You will be able to cancel or reschedule them.</p>
            </div>
            <div id="history" class="ochd-tab-content">
                <h3>Booking History</h3>
                <p>A list of all your past appointments and service orders.</p>
            </div>
            <div id="invoices" class="ochd-tab-content">
                <h3>Invoices</h3>
                <p>Here you will find all your invoices. You can view their status and pay for them.</p>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
