<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class OCHandyDude_Mobile_Menu {

    public function __construct() {
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
    }

    public function enqueue_assets() {
        wp_enqueue_style( 'ochandydude-mobile-menu-style', OCHD_MASTER_PLUGIN_URL . 'assets/css/mobile-menu.css', [], '4.2' );
        wp_enqueue_script( 'ochandydude-mobile-menu-script', OCHD_MASTER_PLUGIN_URL . 'assets/js/mobile-menu.js', [], '4.2', true );
        wp_enqueue_script( 'ochandydude-header-icon-manager', OCHD_MASTER_PLUGIN_URL . 'assets/js/header-icon-manager.js', ['ochandydude-mobile-menu-script'], '1.0', true );
        wp_localize_script( 'ochandydude-mobile-menu-script', 'ochdMobileMenu', [ 'ajax_url' => admin_url( 'admin-ajax.php' ) ] );
    }

    public function register_menu_location() {
        register_nav_menu( 'ochd_main_header_menu', 'OCHD Main Header Menu' );
    }

    public function get_main_menu_html() {
        $menu_items = wp_get_nav_menu_items(get_nav_menu_locations()['ochd_main_header_menu']);

        if (empty($menu_items)) {
            echo '<div class="ochd-menu-button"><a class="ochd-menu-button__link" href="#">Please assign a menu</a></div>';
            wp_die();
        }

        $menu_html = '';
        foreach ($menu_items as $item) {
            $classes = 'ochd-menu-button ' . implode(' ', $item->classes);
            $menu_html .= sprintf(
                '<div class="%s"><a href="%s" class="ochd-menu-button__link">%s</a></div>',
                esc_attr($classes),
                esc_url($item->url),
                esc_html($item->title)
            );
        }

        echo $menu_html;
        wp_die();
    }
}
