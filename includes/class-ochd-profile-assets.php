<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class OCHandyDude_Profile_Assets {

    public function __construct() {
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_assets' ] );
    }

    /**
     * Enqueues CSS and JS files and localizes script data.
     */
    public function enqueue_assets() {
        // ����� ��� ���� �������
        wp_enqueue_style(
            'ochandydude-profile-style',
            OCHD_MASTER_PLUGIN_URL . 'assets/css/profile-menu.css',
            [],
            '3.4' // ��������� ������
        );
        
        // ������ ��� ���� �������
        wp_enqueue_script(
            'ochandydude-profile-menu-script',
            OCHD_MASTER_PLUGIN_URL . 'assets/js/profile-menu.js',
            [],
            '1.1', // ��������� ������
            true
        );

        // ���������: �������� ������ �� PHP � ��� ������ profile-menu.js
        $profile_data = [
            'isLoggedIn' => is_user_logged_in(),
            'loginUrl'   => esc_url(do_shortcode('[openid_connect_generic_auth_url]')),
            'profileUrl' => is_user_logged_in() ? esc_url(get_permalink(get_option('ochd_profile_page_id'))) : ''
        ];
        wp_localize_script( 'ochandydude-profile-menu-script', 'ochdProfileData', $profile_data );
    }
}