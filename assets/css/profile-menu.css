/* OCHandyDude Profile Menu Styles v4.0 (Final & Stable) */

:root {
    --ochd-bg-light: rgba(255, 255, 255, 0.85);
    --ochd-bg-dark: rgba(40, 40, 40, 0.85);
    --ochd-text-light: #333;
    --ochd-text-dark: #eee;
    --ochd-border-light: #e5e5e5;
    --ochd-border-dark: #555;
    --ochd-hover-light: #f0f0f0;
    --ochd-hover-dark: rgba(255, 255, 255, 0.1);
    --ochd-primary-color: #0073e6;
}

/* --- ��������� ����� ���� --- */
.ochd-profile-menu {
    position: relative;
}

/* --- ������ ������� (SVG) --- */
.ochd-profile-icon {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}
/* ����� SVG ������ ����������� �� mobile-menu.css ����� ����� ����� */

/* Enhanced active state to match standardized hover effects */
.ochd-profile-menu.is-active .ochd-profile-icon svg {
    stroke: #007bff;
    transform: scale(1.1) rotate(90deg);
}

/* Active state background highlight */
.ochd-profile-menu.is-active {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
    transform: translateY(-1px);
}

/* --- ���������� ���� --- */
.ochd-profile-dropdown {
    display: block;
    visibility: hidden;
    opacity: 0;
    position: absolute;
    top: calc(100% + 15px); /* ������ �� ������ */
    right: 0;
    min-width: 240px;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--ochd-border-light);
    background-color: var(--ochd-bg-light);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transform: translateY(-10px);
    transition: opacity 0.2s ease-out, transform 0.2s ease-out, visibility 0.2s;
}

/* ���� ���������� ������ ����� ������� ����� .is-active */
.ochd-profile-menu.is-active .ochd-profile-dropdown {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

/* --- ���������� ����������� ���� --- */
.ochd-profile-dropdown a { color: var(--ochd-text-light); padding: 12px 18px; text-decoration: none; display: block; text-align: left; font-size: 14px; transition: background-color 0.2s; }
.ochd-profile-dropdown a:hover { background-color: var(--ochd-hover-light); }
.ochd-dropdown-header { padding: 16px 18px; border-bottom: 1px solid var(--ochd-border-light); }
.ochd-dropdown-header strong { display: block; font-size: 15px; color: var(--ochd-text-light); }
.ochd-dropdown-header small { color: #777; }
.ochd-dropdown-footer { border-top: 1px solid var(--ochd-border-light); }
.ochd-guest-menu { padding: 15px; display: flex; }
.ochd-guest-login-button { flex-grow: 1; display: block; box-sizing: border-box; padding: 12px; border-radius: 8px; background-color: var(--ochd-primary-color); color: white !important; text-align: center; font-weight: 500; transition: all 0.2s ease-out; }
.ochd-guest-login-button:hover { background-color: #338dff; color: white !important; transform: scale(1.03); }

/* --- ��������� ��� ������ ���� --- */
@media (prefers-color-scheme: dark) {
    .ochd-profile-dropdown { background-color: var(--ochd-bg-dark); border-color: var(--ochd-border-dark); }
    .ochd-profile-dropdown a { color: var(--ochd-text-dark); }
    .ochd-profile-dropdown a:hover { background-color: var(--ochd-hover-dark); }
    .ochd-dropdown-header, .ochd-dropdown-footer { border-color: var(--ochd-border-dark); }
    .ochd-dropdown-header strong { color: var(--ochd-text-dark); }
    .ochd-dropdown-header small { color: #aaa; }
}

/* --- ����� ��� ������� ������� � ������������ --- */
.ochd-profile-card, .ochd-tabs, .ochd-tab-content { border: 1px solid var(--ochd-border-light); border-radius: 8px; padding: 20px; margin-bottom: 20px; }
.ochd-tabs { overflow: hidden; border-bottom: none; border-radius: 8px 8px 0 0; padding: 0; }
.ochd-tab-link { background-color: inherit; float: left; border: none; outline: none; cursor: pointer; padding: 14px 16px; transition: 0.3s; font-size: 16px; border-right: 1px solid var(--ochd-border-light); color: var(--ochd-text-light); }
.ochd-tab-link:last-child { border-right: none; }
.ochd-tab-link:hover { background-color: var(--ochd-hover-light); }
.ochd-tab-link.active { background-color: var(--ochd-hover-light); font-weight: bold; }
.ochd-tab-content { display: none; border-top: none; border-radius: 0 0 8px 8px; }

@media (prefers-color-scheme: dark) {
    .ochd-profile-card, .ochd-tabs, .ochd-tab-content { border-color: var(--ochd-border-dark); }
    .ochd-tab-link { border-color: var(--ochd-border-dark); color: var(--ochd-text-dark); }
    .ochd-tab-link:hover, .ochd-tab-link.active { background-color: var(--ochd-hover-dark); }
}