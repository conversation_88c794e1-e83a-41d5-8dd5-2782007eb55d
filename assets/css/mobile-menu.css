/* OCHandyDude Header & Menu Styles v5.3 (Final Responsive) */

/* --- ������� ����� � ���� ������� ��� ������ --- */
body { background-attachment: fixed; background-size: cover; background-repeat: no-repeat; animation: gentle-drift 400s infinite alternate ease-in-out; }
@keyframes gentle-drift { from { background-position: top left; } to { background-position: bottom right; } }
.easyappointments-iframe { border-radius: 80px; overflow: hidden; background-color: transparent; }
main.wp-block-group[style*="margin-top"] { margin-top: 25px !important; }
.wp-block-group.is-content-justification-space-between { padding: 10px 20px !important; box-sizing: border-box; }

/* --- ����� ��� ��������������� ������ � ����� --- */
.ochd-unified-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}
.ochd-unified-icon-wrapper + .ochd-unified-icon-wrapper { margin-left: 12px; }
.ochd-unified-icon-wrapper svg {
    width: 30px;
    height: 30px;
    stroke: #fff;
    stroke-width: 1.8;
    fill: none;
    transition: all 0.3s ease;
}

/* Standardized hover effect for all icons */
.ochd-unified-icon-wrapper:hover svg {
    stroke: #007bff;
    transform: scale(1.1);
}

/* Enhanced hover effect with background highlight */
.ochd-unified-icon-wrapper:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
    transform: translateY(-1px);
}

/* Focus states for accessibility */
.ochd-unified-icon-wrapper:focus,
.ochd-unified-icon-wrapper:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
}

.ochd-unified-icon-wrapper:focus svg,
.ochd-unified-icon-wrapper:focus-visible svg {
    stroke: #007bff;
    transform: scale(1.1);
}

/* Active state for clicked icons */
.ochd-icon-active {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-radius: 6px !important;
    transform: translateY(-1px) !important;
}

.ochd-icon-active svg {
    stroke: #007bff !important;
    transform: scale(1.1) !important;
}

/* Active state for mobile menu toggle */
.ochd-mobile-menu-toggle.ochd-icon-active span {
    background-color: #007bff !important;
}

/* Cart icon specific styling */
.ochd-cart-icon {
    position: relative;
}

/* Ensure cart icon gets the same hover treatment */
.ochd-cart-icon:hover svg {
    stroke: #007bff;
    transform: scale(1.1);
}

.ochd-cart-icon:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
    transform: translateY(-1px);
}

.wc-block-mini-cart__button { display: none !important; } /* �������� ������������ ������� */

/* --- ����� ��� ������-������� --- */
.ochd-mobile-menu-toggle {
    display: flex;
    width: 30px;
    height: 30px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background: transparent;
    border: none;
    padding: 0;
    gap: 5px;
    z-index: 1002;
    transition: all 0.3s ease;
    border-radius: 6px;
}

/* Standardized hover effect matching other icons */
.ochd-mobile-menu-toggle:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.1) translateY(-1px);
}

.ochd-mobile-menu-toggle:hover span {
    background-color: #007bff;
}

/* Focus states for accessibility */
.ochd-mobile-menu-toggle:focus,
.ochd-mobile-menu-toggle:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    background-color: rgba(0, 123, 255, 0.1);
}

.ochd-mobile-menu-toggle:focus span,
.ochd-mobile-menu-toggle:focus-visible span {
    background-color: #007bff;
}

.ochd-mobile-menu-toggle span {
    display: block;
    width: 90%;
    height: 2.5px;
    background-color: #fff;
    border-radius: 3px;
    transition: all 0.3s ease;
    transform-origin: center;
}
.ochd-mobile-menu-toggle.is-open { transform: rotate(180deg); }
.ochd-mobile-menu-toggle.is-open span:nth-child(1) { transform: translateY(7.5px) rotate(45deg); }
.ochd-mobile-menu-toggle.is-open span:nth-child(2) { opacity: 0; }
.ochd-mobile-menu-toggle.is-open span:nth-child(3) { transform: translateY(-7.5px) rotate(-45deg); }

/* --- ����� ��� ������ ���������� ���� --- */
.ochd-main-menu-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px 0;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out, opacity 0.4s ease-in-out, padding 0.4s ease-in-out;
}
.ochd-main-menu-container.is-open {
    max-height: 500px;
    opacity: 1;
}

/* --- ����� ��� ������ ������ ������ ���� --- */
.ochd-menu-button__link {
    display: block;
    border-radius: 9999px;
    border-width: 2px;
    border-style: solid;
    padding: 10px 25px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    color: white !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    background: transparent !important;
}
.ochd-menu-button__link:hover {
    border-color: white !important;
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-3px);
}

/* ����� ��� ���������� ������ (CTA) */
.ochd-menu-button.cta-button .ochd-menu-button__link {
    background: linear-gradient(29deg,rgb(6,147,227) 3%,rgb(155,81,224) 100%) !important;
    border-color: transparent !important;
    transform: scale(1.1);
}
.ochd-menu-button.cta-button .ochd-menu-button__link:hover {
    transform: scale(1.15) translateY(-3px);
    box-shadow: 0px 5px 15px rgba(155, 81, 224, 0.4);
}

/* ================================================================= */
/* --- �����������: ���������� ������� ������������ ��� ����� ������� --- */
/* ================================================================= */

/* ��� ��������� ��������� � ������� ��������� */
@media (max-width: 780px) {
    .ochd-menu-button__link {
        padding: 10px 20px;
        font-size: 15px;
    }
    .ochd-menu-button.cta-button .ochd-menu-button__link {
        transform: scale(1.05); /* ������ ��������� CTA, ����� �� ����������� */
    }
    .ochd-menu-button.cta-button .ochd-menu-button__link:hover {
        transform: scale(1.1) translateY(-3px);
    }
}

/* ��� ����������� ��������� */
@media (max-width: 525px) {
    .ochd-main-menu-container {
        gap: 8px; /* ��������� ���������� ����� �������� */
    }
    .ochd-menu-button__link {
        font-size: 14px;
        padding: 8px 15px;
    }
}

/* ��� ����� ����� ��������� */
@media (max-width: 430px) {
    .ochd-menu-button__link {
        font-size: 12px;
        padding: 8px 12px;
    }
}

/* ��� ����� ����� ����� ��������� */
@media (max-width: 365px) {
    .ochd-menu-button__link {
        font-size: 10px;
        padding: 8px 10px;
    }
}
