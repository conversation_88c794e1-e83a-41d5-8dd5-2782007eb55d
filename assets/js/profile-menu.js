document.addEventListener('DOMContentLoaded', function() {
    const profileMenu = document.querySelector('.ochd-profile-menu');
    if (!profileMenu) return;

    const profileIcon = profileMenu.querySelector('.ochd-profile-icon');
    if (!profileIcon) return;

    // ������� � ������� ������������� ��� ���� ���������
    profileIcon.addEventListener('click', function(event) {
        // ������������� ����� �������� �� ��������� (��������, ������� �� ������)
        event.preventDefault();
        // ������������� "��������", ����� �� �������� ���������� �� ���������
        event.stopPropagation();
        // ������ ����������� ����� 'is-active'
        profileMenu.classList.toggle('is-active');
    });

    // ��������� ���� �� ����� ��� ��� �������
    document.addEventListener('pointerdown', function(event) {
        if (profileMenu.classList.contains('is-active') && !profileMenu.contains(event.target)) {
            profileMenu.classList.remove('is-active');
        }
    });
});