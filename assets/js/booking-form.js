/**
 * OCHandyDude Smart Booking Wrapper Script v12.0 (Stable Production)
 */
document.addEventListener('DOMContentLoaded', function() {
    const guestButton = document.getElementById('ochd-continue-as-guest');
    const guestOverlay = document.getElementById('ochd-guest-overlay');
    if (guestButton) {
        guestButton.addEventListener('click', function(e) {
            e.preventDefault();
            guestOverlay.style.opacity = '0';
            setTimeout(() => { guestOverlay.style.display = 'none'; }, 300);
        });
    }

    window.addEventListener('message', (event) => {
        const wrapper = document.querySelector('.ochd-booking-form-wrapper');
        if (!wrapper) return;
        const eaIframe = wrapper.querySelector('iframe.easyappointments-iframe');

        if (!eaIframe || event.source !== eaIframe.contentWindow) { return; }

        const data = event.data;

        // Handle ready message from iframe
        if (data && data.source === 'OchdIframe' && data.status === 'ready') {
            if (typeof ochdBookingData === 'undefined') {
                console.error('ochdBookingData not available in parent window');
                return;
            }

            console.log('Sending data to iframe:', ochdBookingData);
            const targetOrigin = new URL(eaIframe.src).origin;
            const message = {
                source: 'OCHandyDudeParent',
                isLoggedIn: ochdBookingData.isLoggedIn,
                loginUrl: ochdBookingData.login_url,
                ajaxUrl: ochdBookingData.ajax_url,
                userData: ochdBookingData.userData || null,
                nonce: ochdBookingData.nonce
            };

            eaIframe.contentWindow.postMessage(message, targetOrigin);
        }

        // CRITICAL: Handle redirect request from iframe
        else if (data && data.source === 'OchdIframe' && data.action === 'redirect' && data.url) {
            console.log('Redirect request received from iframe:', data.url);

            try {
                // Perform redirect in parent window
                window.location.href = data.url;
            } catch (error) {
                console.error('Parent window redirect failed:', error);
                // Fallback: try location.replace
                try {
                    window.location.replace(data.url);
                } catch (replaceError) {
                    console.error('Location.replace also failed:', replaceError);
                    // Final fallback: open in new window
                    window.open(data.url, '_self');
                }
            }
        }
    });
});