/**
 * OCHandyDude Header Icon State Manager
 * Manages exclusive activation and proper toggle behavior for all header icons
 */
document.addEventListener('DOMContentLoaded', function() {
    
    // Icon state management
    const IconManager = {
        activeIcon: null,
        
        // Register an icon for state management
        registerIcon: function(element, iconType, toggleCallback) {
            if (!element) return;
            
            element.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                
                // Check if this icon is currently active
                const isCurrentlyActive = this.activeIcon === element;
                
                // Close all icons first
                this.closeAllIcons();
                
                // If it wasn't active, activate it now
                if (!isCurrentlyActive) {
                    this.activateIcon(element, iconType, toggleCallback);
                } else {
                    // If it was active, it's now closed (already handled by closeAllIcons)
                    console.log(`${iconType} icon deactivated`);
                }
            });
        },
        
        // Activate a specific icon
        activateIcon: function(element, iconType, toggleCallback) {
            this.activeIcon = element;
            
            // Apply visual active state
            element.classList.add('ochd-icon-active');
            
            // Execute the icon-specific toggle callback
            if (toggleCallback) {
                toggleCallback(true);
            }
            
            console.log(`${iconType} icon activated`);
        },
        
        // Close all icons
        closeAllIcons: function() {
            // Remove active state from all icons
            document.querySelectorAll('.ochd-icon-active').forEach(icon => {
                icon.classList.remove('ochd-icon-active');
            });
            
            // Close profile dropdown
            const profileMenu = document.querySelector('.ochd-profile-menu');
            if (profileMenu) {
                profileMenu.classList.remove('is-active');
            }
            
            // Close mobile menu
            const menuToggle = document.querySelector('.ochd-mobile-menu-toggle');
            const menuContainer = document.querySelector('.ochd-main-menu-container');
            if (menuToggle && menuContainer) {
                menuToggle.classList.remove('is-open');
                menuContainer.classList.remove('is-open');
            }
            
            // Reset active icon
            this.activeIcon = null;
        },
        
        // Initialize all header icons
        init: function() {
            // Wait for other scripts to initialize first
            setTimeout(() => {
                this.initializeIcons();
            }, 100);
        },

        initializeIcons: function() {
            // Profile menu icon - override existing handler
            const profileIcon = document.querySelector('.ochd-profile-icon');
            if (profileIcon) {
                // Remove existing event listeners by cloning
                const newProfileIcon = profileIcon.cloneNode(true);
                profileIcon.parentNode.replaceChild(newProfileIcon, profileIcon);

                this.registerIcon(newProfileIcon.parentElement, 'profile', (activate) => {
                    const profileMenu = document.querySelector('.ochd-profile-menu');
                    if (profileMenu && activate) {
                        profileMenu.classList.add('is-active');
                    }
                });
            }

            // Mobile menu toggle - override existing handler
            const menuToggle = document.querySelector('.ochd-mobile-menu-toggle');
            if (menuToggle) {
                // Remove existing event listeners by cloning
                const newMenuToggle = menuToggle.cloneNode(true);
                menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);

                this.registerIcon(newMenuToggle, 'menu', (activate) => {
                    const menuContainer = document.querySelector('.ochd-main-menu-container');
                    if (activate && menuContainer) {
                        newMenuToggle.classList.add('is-open');
                        menuContainer.classList.add('is-open');
                    }
                });
            }

            // Cart icon - override existing handler
            const cartIcon = document.querySelector('.ochd-cart-icon');
            if (cartIcon) {
                // Remove existing event listeners by cloning
                const newCartIcon = cartIcon.cloneNode(true);
                cartIcon.parentNode.replaceChild(newCartIcon, cartIcon);

                this.registerIcon(newCartIcon, 'cart', (activate) => {
                    if (activate) {
                        // Trigger the WooCommerce cart
                        const miniCart = document.querySelector('.wc-block-mini-cart');
                        if (miniCart) {
                            const cartButton = miniCart.querySelector('button');
                            if (cartButton) {
                                // Small delay to allow state to be set first
                                setTimeout(() => {
                                    cartButton.click();
                                }, 50);
                            }
                        }
                    }
                });
            }

            // Close icons when clicking outside (override existing handlers)
            document.addEventListener('pointerdown', (event) => {
                const headerIcons = document.querySelector('.ochd-profile-menu, .wc-block-mini-cart')?.parentNode;
                if (headerIcons && !headerIcons.contains(event.target)) {
                    this.closeAllIcons();
                }
            });

            console.log('Header Icon Manager initialized with exclusive activation');
        }
    };
    
    // Initialize the icon manager
    IconManager.init();
    
    // Make it globally available for debugging
    window.OCHDIconManager = IconManager;
});
