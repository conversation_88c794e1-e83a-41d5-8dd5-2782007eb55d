document.addEventListener('DOMContentLoaded', function() {
    const mainHeader = document.querySelector('.wp-block-group.is-content-justification-space-between');
    const iconsContainer = document.querySelector('.ochd-profile-menu, .wc-block-mini-cart')?.parentNode;

    if (!mainHeader || !iconsContainer) {
        console.warn('OCHandyDude Menu: Required header elements not found.');
        return;
    }

    // --- 1. ������� ��������� ��� ������ ������ ���� ---
    const menuContainer = document.createElement('nav');
    menuContainer.className = 'ochd-main-menu-container';
    // ��������� ��������� ����� �����
    mainHeader.parentNode.insertBefore(menuContainer, mainHeader.nextSibling);

    // --- 2. ��������� HTML ���� � ������� AJAX ---
    fetch(ochdMobileMenu.ajax_url + '?action=ochd_get_main_menu')
        .then(response => response.text())
        .then(html => {
            menuContainer.innerHTML = html;
        });
        
    // --- 3. ������� ������ "������" � ��������� �� ---
    const toggleButton = document.createElement('button');
    toggleButton.className = 'ochd-mobile-menu-toggle';
    toggleButton.setAttribute('aria-label', 'Toggle Menu');
    toggleButton.innerHTML = '<span></span><span></span><span></span>';
    iconsContainer.prepend(toggleButton);

    // --- 4. �������� ������ ������� �� ��������� (���������� ����������) ---
    const miniCart = document.querySelector('.wc-block-mini-cart');
    if (miniCart) {
        const customCartIcon = document.createElement('a');
        customCartIcon.href = '/cart';
        customCartIcon.className = 'ochd-unified-icon-wrapper ochd-cart-icon';
        customCartIcon.setAttribute('aria-label', 'View Cart');
        customCartIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path></svg>`;
        miniCart.parentNode.insertBefore(customCartIcon, miniCart.nextSibling);
        customCartIcon.addEventListener('click', function(e) {
            e.preventDefault();
            miniCart.querySelector('button').click();
        });
    }

    // --- 5. ������������ ���� �� ������� ---
    toggleButton.addEventListener('click', function() {
        this.classList.toggle('is-open');
        menuContainer.classList.toggle('is-open');
    });
});