<?php
/**
 * Plugin Name:       OCHandyDude Master Plugin
 * Description:       A unified plugin for profile enhancements, smart booking, and SSO integration.
 * Version:           1.1
 * Author:            OCHandyDude & Gemini Architect
 */

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly.

final class OCHandyDude_Master_Plugin {

    private static $instance = null;

    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->define_constants();
        $this->load_dependencies();
        $this->initialize_hooks();
    }

    private function define_constants() {
        define( 'OCHD_MASTER_PLUGIN_PATH', plugin_dir_path( __FILE__ ) );
        define( 'OCHD_MASTER_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
    }

    private function load_dependencies() {
        require_once OCHD_MASTER_PLUGIN_PATH . 'includes/class-ochd-profile-shortcodes.php';
        require_once OCHD_MASTER_PLUGIN_PATH . 'includes/class-ochd-profile-assets.php';
        require_once OCHD_MASTER_PLUGIN_PATH . 'includes/class-ochd-plugin-activation.php';
        require_once OCHD_MASTER_PLUGIN_PATH . 'includes/class-ochd-mobile-menu.php';
        require_once OCHD_MASTER_PLUGIN_PATH . 'includes/class-ochd-smart-booking.php';
        require_once OCHD_MASTER_PLUGIN_PATH . 'includes/class-ochd-sso-integration.php';
    }

    private function initialize_hooks() {
        register_activation_hook( __FILE__, [ 'OCHD_Plugin_Activation', 'create_plugin_pages' ] );

        new OCHandyDude_Profile_Shortcodes();
        new OCHandyDude_Profile_Assets();

        // Initialize mobile menu with proper hook registration
        $mobile_menu = new OCHandyDude_Mobile_Menu();
        add_action( 'after_setup_theme', [ $mobile_menu, 'register_menu_location' ] );
        add_action( 'wp_ajax_ochd_get_main_menu', [ $mobile_menu, 'get_main_menu_html' ] );
        add_action( 'wp_ajax_nopriv_ochd_get_main_menu', [ $mobile_menu, 'get_main_menu_html' ] );

        OCHD_Smart_Booking::get_instance();
        new OCHD_SSO_Integration();
    }
}

add_action( 'plugins_loaded', [ 'OCHandyDude_Master_Plugin', 'get_instance' ] );